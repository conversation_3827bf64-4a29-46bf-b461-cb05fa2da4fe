#!/bin/bash

# Diagnóstico completo de Moodle - Configuración para cualquier IP
echo "🔍 DIAGNÓSTICO COMPLETO DE MOODLE"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Fecha: $(date)"
echo ""

# 1. Estado de la red
echo "1. 🌐 CONFIGURACIÓN DE RED:"
echo "   Interfaces de red activas:"
ip addr show | grep -E "^[0-9]+:|inet " | grep -A1 "state UP" | sed 's/^/   /'
echo ""

# 2. Conectividad básica
echo "2. 🔗 PRUEBAS DE CONECTIVIDAD:"
echo -n "   Router (***********): "
if ping -c 1 -W 2 *********** >/dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ FALLO"
fi

echo -n "   Internet (*******): "
if ping -c 1 -W 2 ******* >/dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ FALLO"
fi

echo -n "   Localhost: "
if ping -c 1 -W 2 127.0.0.1 >/dev/null 2>&1; then
    echo "✅ OK"
else
    echo "❌ FALLO"
fi
echo ""

# 3. Servicios del sistema
echo "3. 🔧 ESTADO DE SERVICIOS:"
services=("apache2" "mysql" "networking")
for service in "${services[@]}"; do
    status=$(systemctl is-active $service 2>/dev/null)
    if [ "$status" = "active" ]; then
        echo "   $service: ✅ $status"
    else
        echo "   $service: ❌ $status"
    fi
done
echo ""

# 4. Puertos en escucha
echo "4. 🔌 PUERTOS EN ESCUCHA:"
echo "   Puerto 80 (HTTP): $(ss -tlnp | grep ':80 ' | wc -l) conexiones"
echo "   Puerto 3306 (MySQL): $(ss -tlnp | grep ':3306 ' | wc -l) conexiones"
echo ""

# 5. Prueba de acceso a Moodle
echo "5. 🎯 PRUEBA DE ACCESO A MOODLE:"
current_ip=$(ip route get ******* | grep -oP 'src \K\S+' 2>/dev/null)
if [ -n "$current_ip" ]; then
    echo "   IP detectada: $current_ip"
    echo -n "   Acceso HTTP: "
    if wget --spider --timeout=5 "http://$current_ip/moodle" >/dev/null 2>&1; then
        echo "✅ OK"
        echo "   URL funcional: http://$current_ip/moodle"
    else
        echo "❌ FALLO"
    fi
else
    echo "   ❌ No se pudo detectar IP"
fi
echo ""

# 6. Configuración de Moodle
echo "6. 📝 CONFIGURACIÓN DE MOODLE:"
if [ -f "/var/www/html/moodle/config.php" ]; then
    echo "   ✅ Archivo config.php existe"
    echo "   Configuración wwwroot:"
    sudo grep -n "wwwroot" /var/www/html/moodle/config.php | head -3 | sed 's/^/      /'
else
    echo "   ❌ Archivo config.php NO encontrado"
fi
echo ""

# 7. Permisos y directorios
echo "7. 🔐 PERMISOS Y DIRECTORIOS:"
dirs=("/var/www/html/moodle" "/var/www/moodledata")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        perms=$(ls -ld "$dir" | awk '{print $1 " " $3 ":" $4}')
        echo "   ✅ $dir: $perms"
    else
        echo "   ❌ $dir: NO EXISTE"
    fi
done
echo ""

# 8. Configuración de Apache
echo "8. ⚙️  CONFIGURACIÓN DE APACHE:"
echo "   Virtual Hosts configurados:"
sudo apache2ctl -S 2>/dev/null | grep "port 80" | sed 's/^/      /'
echo ""

# 9. Logs recientes
echo "9. 📋 LOGS RECIENTES (últimas 3 líneas):"
echo "   Apache Error Log:"
sudo tail -3 /var/log/apache2/error.log 2>/dev/null | sed 's/^/      /' || echo "      Sin errores recientes"
echo ""

# 10. Resumen y recomendaciones
echo "10. 📊 RESUMEN:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Verificar si todo está OK
all_ok=true

# Verificar servicios críticos
for service in "apache2" "mysql"; do
    if ! systemctl is-active $service >/dev/null 2>&1; then
        all_ok=false
        break
    fi
done

# Verificar conectividad
if ! ping -c 1 -W 2 *********** >/dev/null 2>&1; then
    all_ok=false
fi

# Verificar acceso a Moodle
current_ip=$(ip route get ******* | grep -oP 'src \K\S+' 2>/dev/null)
if [ -n "$current_ip" ]; then
    if ! wget --spider --timeout=5 "http://$current_ip/moodle" >/dev/null 2>&1; then
        all_ok=false
    fi
else
    all_ok=false
fi

if [ "$all_ok" = true ]; then
    echo "🎉 ¡CONFIGURACIÓN COMPLETAMENTE FUNCIONAL!"
    echo ""
    echo "📱 INSTRUCCIONES PARA ESTUDIANTES:"
    echo "   1. Conectarse al WiFi de la institución"
    echo "   2. Abrir navegador web"
    echo "   3. Ir a: http://$current_ip/moodle"
    echo ""
    echo "✅ Los estudiantes pueden acceder desde cualquier dispositivo"
    echo "✅ La configuración es dinámica y se adapta a cambios de IP"
else
    echo "⚠️  HAY ALGUNOS PROBLEMAS QUE REVISAR"
    echo ""
    echo "🔧 ACCIONES RECOMENDADAS:"
    echo "   • Verificar que todos los servicios estén activos"
    echo "   • Comprobar la conectividad de red"
    echo "   • Revisar los logs de Apache para errores"
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Diagnóstico completado - $(date)"
